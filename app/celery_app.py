"""Celery Application Configuration for Media Convert Service"""

from celery import Celery

import app.utils.celery_banner  # noqa: F401
from app.config import RabbitMQConfig
from app.utils.logger import get_logger

logger = get_logger(__name__)

# Initialize Celery app
celery_app = Celery(
    "media_convert",
    broker=RabbitMQConfig.get_broker_url(),
    backend="rpc://",
    include=["app.tasks.microservice_conversion"],
)

# Basic configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    broker_connection_retry_on_startup=True,
)

# Queue routing with project prefix
celery_app.conf.task_routes = {
    "app.tasks.microservice_conversion.*": {"queue": "media-convert.requests"},
}
