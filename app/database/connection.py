"""Database connection configuration for Media Convert Service"""

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import declarative_base, sessionmaker

from app.config import DatabaseConfig
from app.utils.logger import get_logger

logger = get_logger(__name__)

# SQLAlchemy engine with connection pooling
engine = create_engine(
    DatabaseConfig.URL,
    pool_size=DatabaseConfig.POOL_SIZE,
    max_overflow=DatabaseConfig.MAX_OVERFLOW,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=DatabaseConfig.POOL_RECYCLE,  # Recycle connections every hour
    echo=DatabaseConfig.ECHO,  # Log SQL queries in debug mode
)

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class for models
Base = declarative_base()


def get_db_session():
    """
    Get a database session.

    Usage:
        with get_db_session() as session:
            # Use session here
            pass
    """
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logger.error(f"Database session error: {e}")
        raise
    finally:
        session.close()


def test_connection():
    """Test database connection"""
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            return True
    except (SQLAlchemyError, OSError) as e:
        logger.error(f"Database connection failed: {e}")
        return False
