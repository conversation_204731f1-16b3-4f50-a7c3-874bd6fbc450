"""Database models for Media Convert Service"""

from enum import Enum

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String, Text
from sqlalchemy.sql import func

from .connection import Base


class MediaType(str, Enum):
    """Supported media types for conversion"""

    VIDEO = "video"
    IMAGE = "image"
    AUDIO = "audio"


class OutputFormat(str, Enum):
    """Supported output formats"""

    # Video formats
    DASH = "dash"
    HLS = "hls"

    # Image formats
    WEBP = "webp"
    AVIF = "avif"
    JPEG = "jpeg"
    PNG = "png"

    # Audio formats
    MP3 = "mp3"
    AAC = "aac"
    OGG = "ogg"


class MediaConversion(Base):
    """Model for media conversion tracking (video, image, audio, etc.)"""

    __tablename__ = "media_conversions"

    # Primary key - uses conversion_id from message payload
    id = Column(String(255), primary_key=True)

    # Media type (video, image, audio, etc.)
    media_type = Column(String(50), nullable=False, index=True)

    # File paths
    input_path = Column(String(1000), nullable=False)
    output_path = Column(String(1000), nullable=True)

    # Status tracking
    status = Column(String(50), nullable=False, default="PENDING", index=True)
    # Status values: PENDING, PROCESSING, COMPLETED, FAILED

    # Conversion format (for output format: dash, webp, avif, etc.)
    output_format = Column(String(50), nullable=True, index=True)

    # Timestamps
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
    )
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
    )

    # Processing details
    processing_duration_seconds = Column(
        Integer,
        nullable=True,
    )  # Time taken to process conversion
    error_message = Column(Text, nullable=True)

    # Metadata (JSON field for flexible data storage)
    metadata_json = Column(JSON, nullable=True)

    def __repr__(self):
        return (
            f"<MediaConversion(id={self.id}, "
            f"media_type={self.media_type}, status={self.status})>"
        )

    def to_dict(self):
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "media_type": self.media_type,
            "input_path": self.input_path,
            "output_path": self.output_path,
            "status": self.status,
            "output_format": self.output_format,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "processing_duration_seconds": self.processing_duration_seconds,
            "error_message": self.error_message,
            "metadata_json": self.metadata_json,
        }
