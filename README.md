# Media Convert Service

A high-performance media conversion service built with Python, Celery, and FFmpeg. Currently supports video to MPEG-DASH conversion with multiple bitrates and thumbnail generation. Designed to be extensible for future image (WebP, AVIF) and audio conversions.

## Features

### Current Implementation
- **MPEG-DASH Video Conversion**: Convert videos to adaptive streaming format
- **Multiple Bitrates**: Automatic generation of multiple quality levels (720p, 480p, 360p, 240p)
- **Thumbnail Generation**: Create video thumbnails during conversion
- **S3 Integration**: Download from and upload to AWS S3
- **Celery Workers**: Distributed task processing with RabbitMQ
- **Database Persistence**: Track conversion jobs and status in PostgreSQL
- **Comprehensive Logging**: Detailed logging with structured output
- **Error Handling**: Robust error handling and recovery
- **Microservice Communication**: Standardized request-response pattern for microservice integration

### Future Extensions (Architecture Ready)
- **Image Conversion**: WebP, AVIF, JPEG, PNG format support
- **Audio Conversion**: MP3, AAC, OGG format support
- **Extensible Converter System**: Plugin-based architecture for new media types

## Quick Start

```bash
make up      # Start services (PostgreSQL, RabbitMQ)
make worker  # Start Celery worker
```

## Architecture

- **Python 3.12**: Core application with Celery
- **FFmpeg**: Video processing engine
- **RabbitMQ**: Message broker for task distribution (auto-configured)
- **PostgreSQL**: Database for job tracking and persistence
- **AWS S3**: File storage and delivery
- **Docker**: Containerization and orchestration

## Usage

### Usage

This service is designed exclusively for microservice communication using standardized request-response patterns.

### Microservice Communication

For communication between microservices, use the standardized request-response pattern:

```python
from app.models.messages import ConversionRequest

# Create conversion request
request = ConversionRequest.create(
    input_s3_path="s3://bucket/video.mp4",
    output_s3_prefix="s3://bucket/dash/video/",
    reply_to_queue="your-service.responses"
)

# Send request
celery_app.send_task(
    "app.tasks.microservice_conversion.handle_conversion_request_from_dict",
    args=[request.to_dict()],
    queue="media-convert.requests"
)
```

📖 **See [MICROSERVICE_COMMUNICATION.md](MICROSERVICE_COMMUNICATION.md) for complete integration guide.**

## Development

### Docker Commands
```bash
make up            # Start services (PostgreSQL, RabbitMQ)
make worker        # Start worker
make logs          # View worker logs
make down          # Stop services
make build         # Build worker image
make clean         # Complete project reset
```

### Development Commands
```bash
make test          # Run tests (via Poetry)
make lint          # Run linting (via Poetry)
make format        # Format code (via Poetry)
```

## Configuration

Environment variables in `.env`:
- `DATABASE_URL` - PostgreSQL connection string
- `RABBITMQ_HOST/USER/PASSWORD` - RabbitMQ configuration
- `AWS_ACCESS_KEY_ID` / `AWS_SECRET_ACCESS_KEY` - AWS credentials
- `S3_BUCKET` - Target S3 bucket
- `CELERY_QUEUES` - Queue names (default: media-convert.video-conversion,media-convert.notifications)

## Database Tracking

All conversion jobs are automatically tracked in PostgreSQL with:

- **Job ID**: Unique identifier (Celery task ID)
- **Status**: PENDING, PROCESSING, COMPLETED, FAILED
- **Input/Output paths**: S3 URLs for source and destination
- **Duration**: Processing time in seconds
- **Error messages**: Detailed error information for failed jobs
- **Metadata**: Additional conversion details and metrics

## Output Structure

```
s3://bucket/output-prefix/
├── manifest.mpd
├── input_0init.m4s             # 720p
├── input_1init.m4s             # 480p
├── input_2init.m4s             # 360p
├── input_3init.m4s             # 240p
├── input_4init.m4s             # Audio
└── input_thumbnail_*.jpg       # Thumbnails
```

## Monitoring

Check conversion status in database:
```sql
SELECT id, media_type, output_format, status, processing_duration_seconds, error_message, created_at
FROM media_conversions
ORDER BY created_at DESC;
```

## Development

### Dependencies
This project uses Poetry for dependency management:

```bash
# Install dependencies
poetry install

# Add new dependency
poetry add <package>

# Development dependencies
poetry add --group dev <package>
```

### Local Development
```bash
# Activate Poetry environment
poetry shell

# Run locally (without Docker)
poetry run python -m app.celery_app
```

## Testing

### Unit Tests
Run tests with coverage:
```bash
make test-cov
```

Run tests in watch mode:
```bash
make test-watch
```

### Integration Testing
Send a test conversion task to the worker:
```bash
make send-task
```

This will:
- Generate a unique conversion ID
- Send a test video conversion task
- Show the task ID for monitoring
- You can monitor processing with `make logs`

## Code Quality with SonarQube

This project is configured for SonarQube analysis to maintain high code quality standards.

### Usage

```bash
# Generate reports for SonarQube
make sonar-reports

# Run SonarQube analysis
make sonar
```

Make sure to set the `SONAR_TOKEN` environment variable before running the analysis.

### Reports Generated

All reports are organized in the `.reports/` directory:
- **Coverage**: `.reports/coverage.xml` - Code coverage in XML format
- **Test Results**: `.reports/test-results.xml` - Test execution results in JUnit format
- **Code Quality**: `.reports/ruff-report.json` - Ruff linting issues in JSON format
- **HTML Coverage**: `.reports/htmlcov/` - Human-readable coverage report

## Production Deployment

For AWS EKS deployment:
1. Build production image
2. Configure AWS credentials and RDS PostgreSQL
3. Set up RabbitMQ cluster
4. Deploy worker pods with appropriate resource limits
5. Configure auto-scaling based on queue length