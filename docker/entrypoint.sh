#!/bin/bash

# Media Convert Service - Simple Docker Entrypoint
# Starts Celery worker directly with configuration from environment variables

set -e

# Environment variables with defaults
CELERY_APP=${CELERY_APP:-"app.celery_app"}
CELERY_LOGLEVEL=${CELERY_LOGLEVEL:-"info"}
CELERY_QUEUES=${CELERY_QUEUES:-"media-convert.requests"}
CELERY_CONCURRENCY=${CELERY_CONCURRENCY:-2}
CELERY_MAX_TASKS_PER_CHILD=${CELERY_MAX_TASKS_PER_CHILD:-1000}

echo "Starting Media Convert Celery Worker"
echo "Configuration: App=${CELERY_APP}, Queues=${CELERY_QUEUES}, Concurrency=${CELERY_CONCURRENCY}"

# Create directories and set permissions
mkdir -p /var/log/celery /tmp/media-convert
chown -R celery:celery /var/log/celery /tmp/media-convert

# Run database migrations
echo "Running database migrations..."
chmod +x /app/scripts/run-migrations.sh

# Debug: Check current Python environment
echo "Current Python path: $(which python)"
echo "Checking SQLAlchemy installation..."
python -c "import sqlalchemy; print('SQLAlchemy version:', sqlalchemy.__version__)" || echo "SQLAlchemy not found"

/app/scripts/run-migrations.sh

# Start Celery worker with or without autoreload
exec gosu celery celery -A ${CELERY_APP} worker \
    --loglevel=${CELERY_LOGLEVEL} \
    --queues=${CELERY_QUEUES} \
    --concurrency=${CELERY_CONCURRENCY} \
    --max-tasks-per-child=${CELERY_MAX_TASKS_PER_CHILD}