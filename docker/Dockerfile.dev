# Development Dockerfile for Media Convert Service
FROM python:3.12-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    gosu \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Install Poetry
RUN pip install --no-cache-dir poetry==2.1.3

# Configure Poetry
ENV POETRY_NO_INTERACTION=1 \
    POETRY_VENV_IN_PROJECT=0 \
    POETRY_CACHE_DIR=/tmp/poetry_cache \
    POETRY_VIRTUALENVS_CREATE=false

# Create celery user
RUN useradd --create-home --shell /bin/bash --uid 1000 celery

# Set working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /var/log/celery /tmp/media-convert \
    && chown -R celery:celery /var/log/celery /tmp/media-convert

# Copy Poetry configuration files
COPY pyproject.toml poetry.lock* ./

# Install dependencies
RUN poetry install && rm -rf $POETRY_CACHE_DIR

# Copy application code
COPY . .

# Copy and set permissions for entrypoint
COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Set permissions
RUN chown -R celery:celery /app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD poetry run celery -A app.celery_app inspect ping || exit 1

# Use our custom entrypoint
ENTRYPOINT ["/entrypoint.sh"]
