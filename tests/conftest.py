"""
Pytest configuration and fixtures for media conversion tests
"""

import uuid
from unittest.mock import Mock, patch

import pytest

# App imports
from app.models.messages import ConversionRequest, ConversionResponse

# Database imports - only import when needed
try:
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker

    from app.database.models import Base, MediaConversion

    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False


@pytest.fixture
def sample_conversion_id():
    """Generate a valid UUID for testing"""
    return str(uuid.uuid4())


@pytest.fixture
def invalid_conversion_id():
    """Invalid UUID for testing validation"""
    return "invalid-uuid-123"


@pytest.fixture
def sample_conversion_request(sample_conversion_id):
    """Sample conversion request for testing"""
    return ConversionRequest(
        conversion_id=sample_conversion_id,
        input_s3_path="s3://test-bucket/input/video.mp4",
        output_s3_prefix="s3://test-bucket/output/video",
        reply_to_queue="test.response.queue",
    )


@pytest.fixture
def sample_conversion_request_dict(sample_conversion_id):
    """Sample conversion request as dictionary"""
    return {
        "conversion_id": sample_conversion_id,
        "input_s3_path": "s3://test-bucket/input/video.mp4",
        "output_s3_prefix": "s3://test-bucket/output/video",
        "reply_to_queue": "test.response.queue",
    }


@pytest.fixture
def invalid_conversion_request_dict(invalid_conversion_id):
    """Invalid conversion request for testing validation"""
    return {
        "conversion_id": invalid_conversion_id,
        "input_s3_path": "s3://test-bucket/input/video.mp4",
        "output_s3_prefix": "s3://test-bucket/output/video",
        "reply_to_queue": "test.response.queue",
    }


@pytest.fixture
def sample_conversion_response(sample_conversion_id):
    """Sample conversion response for testing"""
    return ConversionResponse.success(
        conversion_id=sample_conversion_id,
        output_s3_url="s3://test-bucket/output/video/manifest.mpd",
    )


@pytest.fixture
def mock_db_session():
    """Mock database session for testing"""
    if not SQLALCHEMY_AVAILABLE:
        # Return a simple mock if SQLAlchemy is not available
        return Mock()

    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    Session = sessionmaker(bind=engine)
    session = Session()

    yield session

    session.close()


@pytest.fixture
def sample_media_conversion(sample_conversion_id):
    """Sample MediaConversion model for testing"""
    if not SQLALCHEMY_AVAILABLE:
        # Return a simple mock if SQLAlchemy is not available
        mock_conversion = Mock()
        mock_conversion.id = sample_conversion_id
        mock_conversion.media_type = "video"
        mock_conversion.input_path = "s3://test-bucket/input/video.mp4"
        mock_conversion.output_path = "s3://test-bucket/output/video/manifest.mpd"
        mock_conversion.status = "COMPLETED"
        mock_conversion.output_format = "dash"
        mock_conversion.processing_duration_seconds = 120
        mock_conversion.metadata_json = '{"test": "data"}'
        return mock_conversion

    return MediaConversion(
        id=sample_conversion_id,
        media_type="video",
        input_path="s3://test-bucket/input/video.mp4",
        output_path="s3://test-bucket/output/video/manifest.mpd",
        status="COMPLETED",
        output_format="dash",
        processing_duration_seconds=120,
        metadata_json='{"test": "data"}',
    )


@pytest.fixture
def mock_s3_client():
    """Mock S3 client for testing"""
    with patch("app.utils.s3_client.S3Client") as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        yield mock_instance


@pytest.fixture
def mock_celery_task():
    """Mock Celery task for testing"""
    mock_task = Mock()
    mock_task.request.id = str(uuid.uuid4())
    return mock_task


@pytest.fixture
def mock_ffmpeg():
    """Mock FFmpeg wrapper for testing"""
    with patch("app.utils.ffmpeg_wrapper.FFmpegWrapper") as mock:
        mock_instance = Mock()
        mock.return_value = mock_instance
        yield mock_instance
