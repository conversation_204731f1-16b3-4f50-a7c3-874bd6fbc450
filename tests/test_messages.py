"""
Tests for message models
"""

import uuid

import pytest

from app.models.messages import ConversionRequest, ConversionResponse


class TestConversionRequest:
    """Tests for ConversionRequest model"""

    def test_create_valid_request(self, sample_conversion_id):
        """Test creating a valid conversion request"""
        request = ConversionRequest(
            conversion_id=sample_conversion_id,
            input_s3_path="s3://test/input.mp4",
            output_s3_prefix="s3://test/output",
            reply_to_queue="test.queue",
        )

        assert request.conversion_id == sample_conversion_id
        assert request.input_s3_path == "s3://test/input.mp4"
        assert request.output_s3_prefix == "s3://test/output"
        assert request.reply_to_queue == "test.queue"

    def test_create_request_with_invalid_uuid_raises_error(self):
        """Test creating request with invalid UUID raises ValueError"""
        with pytest.raises(ValueError) as exc_info:
            ConversionRequest(
                conversion_id="invalid-uuid",
                input_s3_path="s3://test/input.mp4",
                output_s3_prefix="s3://test/output",
                reply_to_queue="test.queue",
            )

        assert "conversion_id must be a valid UUID" in str(exc_info.value)

    def test_from_dict_valid_data(self, sample_conversion_request_dict):
        """Test creating request from valid dictionary"""
        request = ConversionRequest.from_dict(sample_conversion_request_dict)

        assert request.conversion_id == sample_conversion_request_dict["conversion_id"]
        assert request.input_s3_path == sample_conversion_request_dict["input_s3_path"]
        assert (
            request.output_s3_prefix
            == sample_conversion_request_dict["output_s3_prefix"]
        )
        assert (
            request.reply_to_queue == sample_conversion_request_dict["reply_to_queue"]
        )

    def test_from_dict_invalid_uuid_raises_error(self, invalid_conversion_request_dict):
        """Test creating request from dict with invalid UUID raises ValueError"""
        with pytest.raises(ValueError) as exc_info:
            ConversionRequest.from_dict(invalid_conversion_request_dict)

        assert "conversion_id must be a valid UUID" in str(exc_info.value)

    def test_from_dict_missing_field_raises_error(self):
        """Test creating request from dict with missing field raises KeyError"""
        incomplete_dict = {
            "conversion_id": str(uuid.uuid4()),
            "input_s3_path": "s3://test/input.mp4",
            # Missing output_s3_prefix and reply_to_queue
        }

        with pytest.raises(KeyError):
            ConversionRequest.from_dict(incomplete_dict)

    def test_to_dict(self, sample_conversion_request):
        """Test converting request to dictionary"""
        request_dict = sample_conversion_request.to_dict()

        assert request_dict["conversion_id"] == sample_conversion_request.conversion_id
        assert request_dict["input_s3_path"] == sample_conversion_request.input_s3_path
        assert (
            request_dict["output_s3_prefix"]
            == sample_conversion_request.output_s3_prefix
        )
        assert (
            request_dict["reply_to_queue"] == sample_conversion_request.reply_to_queue
        )


class TestConversionResponse:
    """Tests for ConversionResponse model"""

    def test_success_response(self, sample_conversion_id):
        """Test creating a success response"""
        output_url = "s3://test/output/manifest.mpd"
        response = ConversionResponse.success(
            conversion_id=sample_conversion_id, output_s3_url=output_url
        )

        assert response.conversion_id == sample_conversion_id
        assert response.status == "COMPLETED"
        assert response.output_s3_url == output_url
        assert response.error_message is None

    def test_success_response_with_duration(self, sample_conversion_id):
        """Test creating a success response with processing duration"""
        output_url = "s3://test/output/manifest.mpd"

        response = ConversionResponse.success(
            conversion_id=sample_conversion_id, output_s3_url=output_url
        )

        # ConversionResponse doesn't have processing_duration_seconds field
        assert response.status == "COMPLETED"

    def test_failure_response(self, sample_conversion_id):
        """Test creating a failure response"""
        error_message = "Conversion failed due to invalid input"

        response = ConversionResponse.failure(
            conversion_id=sample_conversion_id, error_message=error_message
        )

        assert response.conversion_id == sample_conversion_id
        assert response.status == "FAILED"
        assert response.error_message == error_message
        assert response.output_s3_url is None

    def test_failure_response_with_duration(self, sample_conversion_id):
        """Test creating a failure response with processing duration"""
        error_message = "Conversion failed"

        response = ConversionResponse.failure(
            conversion_id=sample_conversion_id, error_message=error_message
        )

        # ConversionResponse doesn't have processing_duration_seconds field
        assert response.status == "FAILED"

    def test_to_dict_success(self, sample_conversion_response):
        """Test converting success response to dictionary"""
        response_dict = sample_conversion_response.to_dict()

        assert (
            response_dict["conversion_id"] == sample_conversion_response.conversion_id
        )
        assert response_dict["status"] == sample_conversion_response.status
        assert (
            response_dict["output_s3_url"] == sample_conversion_response.output_s3_url
        )
        assert "error_message" in response_dict

    def test_to_dict_failure(self, sample_conversion_id):
        """Test converting failure response to dictionary"""
        error_message = "Test error"
        response = ConversionResponse.failure(
            conversion_id=sample_conversion_id, error_message=error_message
        )

        response_dict = response.to_dict()

        assert response_dict["conversion_id"] == sample_conversion_id
        assert response_dict["status"] == "FAILED"
        assert response_dict["error_message"] == error_message
        assert response_dict["output_s3_url"] is None
