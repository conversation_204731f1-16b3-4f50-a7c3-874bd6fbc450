from unittest.mock import ANY, Mock, patch

import pytest

from app.database.models import MediaType, OutputFormat
from app.services.conversion_service import ConversionService


@pytest.fixture
def sample_ids():
    return {
        "conversion_id": "conv-123",
        "input_s3_path": "s3://bucket/input.mp4",
        "output_s3_prefix": "s3://bucket/output",
        "celery_task_id": "celery-xyz",
    }


def mock_time_sequence():
    """Simula tempo: start=1000, end=1010."""
    for t in [1000, 1010]:
        yield t


@patch("app.services.conversion_service.MediaConversionRepository")
@patch("app.services.conversion_service.S3Client")
@patch("app.services.conversion_service.MediaConverterFactory")
@patch("app.services.conversion_service.tempfile.mkdtemp", return_value="/tmp/conv_dir")
@patch("app.services.conversion_service.shutil.rmtree")
@patch("app.services.conversion_service.time.time", side_effect=[1000, 1010])
def test_execute_conversion_success(
    mock_time,
    mock_rmtree,
    mock_mkdtemp,
    mock_converter_factory,
    mock_s3_client,
    mock_repo,
    sample_ids,
):
    # Mock repository
    mock_repo.create_conversion.return_value = Mock()
    mock_repo.update_status.return_value = True
    mock_repo.get_conversion.return_value = None

    # Mock S3Client
    s3_instance = Mock()
    s3_instance.download_file.return_value = {"status": "success"}
    s3_instance.upload_directory.return_value = {"status": "success"}
    mock_s3_client.return_value = s3_instance

    # Mock Converter
    converter = Mock()
    converter.convert.return_value = {"converted": True}
    mock_converter_factory.get_converter.return_value = converter

    result = ConversionService.execute_conversion(
        **sample_ids, media_type=MediaType.VIDEO, output_format=OutputFormat.DASH
    )

    assert result["status"] == "completed"
    assert result["conversion_id"] == sample_ids["conversion_id"]
    assert result["processing_duration_seconds"] == 10
    mock_repo.create_conversion.assert_called_once()
    mock_repo.update_status.assert_any_call(
        conversion_id=sample_ids["conversion_id"], status="PROCESSING", metadata=ANY
    )
    mock_repo.update_status.assert_any_call(
        conversion_id=sample_ids["conversion_id"],
        status="COMPLETED",
        output_path=f"{sample_ids['output_s3_prefix']}/manifest.mpd",
        processing_duration_seconds=10,
        metadata=ANY,
    )
    mock_rmtree.assert_called_once_with("/tmp/conv_dir")


@patch("app.services.conversion_service.MediaConversionRepository")
def test_execute_conversion_duplicate_record(mock_repo, sample_ids):
    """Simula duplicidade na criação do registro."""
    mock_repo.create_conversion.return_value = None
    mock_repo.get_conversion.return_value = Mock(status="PENDING")

    result = ConversionService.execute_conversion(
        **sample_ids, media_type=MediaType.VIDEO, output_format=OutputFormat.DASH
    )

    assert result["status"] == "duplicate"
    assert "existing_status" in result
    mock_repo.get_conversion.assert_called_once_with(sample_ids["conversion_id"])


@patch("app.services.conversion_service.MediaConversionRepository")
def test_execute_conversion_create_fails(mock_repo, sample_ids):
    """Simula falha no banco ao criar conversão."""
    mock_repo.create_conversion.return_value = None
    mock_repo.get_conversion.return_value = None

    result = ConversionService.execute_conversion(
        **sample_ids, media_type=MediaType.VIDEO, output_format=OutputFormat.DASH
    )

    assert result["status"] == "failed"
    assert "error" in result


@patch("app.services.conversion_service.MediaConversionRepository")
@patch("app.services.conversion_service.S3Client")
@patch("app.services.conversion_service.MediaConverterFactory")
@patch("app.services.conversion_service.tempfile.mkdtemp", return_value="/tmp/conv_dir")
@patch("app.services.conversion_service.shutil.rmtree")
@patch("app.services.conversion_service.time.time", side_effect=[1000, 1010, 1010])
def test_execute_conversion_download_failure(
    mock_time,
    mock_rmtree,
    mock_mkdtemp,
    mock_converter_factory,
    mock_s3_client,
    mock_repo,
    sample_ids,
):
    """Simula falha no download do S3."""
    mock_repo.create_conversion.return_value = Mock()
    mock_repo.update_status.return_value = True
    mock_repo.get_conversion.return_value = None

    s3_instance = Mock()
    s3_instance.download_file.return_value = {"status": "error", "error": "NotFound"}
    mock_s3_client.return_value = s3_instance
    mock_converter_factory.get_converter.return_value = Mock()

    result = ConversionService.execute_conversion(
        **sample_ids, media_type=MediaType.VIDEO, output_format=OutputFormat.DASH
    )

    assert result["status"] == "failed"
    assert "error" in result
    mock_repo.update_status.assert_any_call(
        conversion_id=sample_ids["conversion_id"],
        status="FAILED",
        processing_duration_seconds=10,
        error_message=ANY,
        metadata=ANY,
    )
    mock_rmtree.assert_called_once_with("/tmp/conv_dir")
