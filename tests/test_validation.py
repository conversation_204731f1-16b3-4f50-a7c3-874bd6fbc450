"""
Tests for validation utilities
"""

import uuid

import pytest

from app.utils.validation import is_valid_uuid, validate_conversion_id


class TestIsValidUuid:
    """Tests for is_valid_uuid function"""

    def test_valid_uuid_string(self):
        """Test with valid UUID string"""
        valid_uuid = str(uuid.uuid4())
        assert is_valid_uuid(valid_uuid) is True

    def test_valid_uuid_object(self):
        """Test with valid UUID object"""
        valid_uuid = uuid.uuid4()
        assert is_valid_uuid(valid_uuid) is True

    def test_invalid_uuid_string(self):
        """Test with invalid UUID string"""
        invalid_uuid = "invalid-uuid-123"
        assert is_valid_uuid(invalid_uuid) is False

    def test_empty_string(self):
        """Test with empty string"""
        assert is_valid_uuid("") is False

    def test_none_value(self):
        """Test with None value"""
        assert is_valid_uuid(None) is False

    def test_integer_value(self):
        """Test with integer value"""
        assert is_valid_uuid(123) is False

    def test_malformed_uuid(self):
        """Test with malformed UUID"""
        malformed_uuid = "550e8400-e29b-41d4-a716"  # Missing part
        assert is_valid_uuid(malformed_uuid) is False

    def test_uuid_with_extra_characters(self):
        """Test with UUID containing extra characters"""
        uuid_with_extra = "550e8400-e29b-41d4-a716-************-extra"
        assert is_valid_uuid(uuid_with_extra) is False


class TestValidateConversionId:
    """Tests for validate_conversion_id function"""

    def test_valid_conversion_id(self):
        """Test with valid conversion ID - should not raise exception"""
        valid_uuid = str(uuid.uuid4())
        # Should not raise any exception
        validate_conversion_id(valid_uuid)

    def test_invalid_conversion_id_raises_error(self):
        """Test with invalid conversion ID - should raise ValueError"""
        invalid_uuid = "invalid-uuid-123"

        with pytest.raises(ValueError) as exc_info:
            validate_conversion_id(invalid_uuid)

        assert "conversion_id must be a valid UUID" in str(exc_info.value)
        assert "invalid-uuid-123" in str(exc_info.value)

    def test_empty_conversion_id_raises_error(self):
        """Test with empty conversion ID"""
        with pytest.raises(ValueError) as exc_info:
            validate_conversion_id("")

        assert "conversion_id must be a valid UUID" in str(exc_info.value)

    def test_none_conversion_id_raises_error(self):
        """Test with None conversion ID"""
        with pytest.raises(ValueError):
            validate_conversion_id(None)

    def test_error_message_format(self):
        """Test that error message contains the invalid value"""
        invalid_value = "test-invalid-value"

        with pytest.raises(ValueError) as exc_info:
            validate_conversion_id(invalid_value)

        error_message = str(exc_info.value)
        assert "conversion_id must be a valid UUID" in error_message
        assert invalid_value in error_message
