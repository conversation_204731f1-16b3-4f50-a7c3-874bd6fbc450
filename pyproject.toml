[tool.poetry]
name = "media-convert"
version = "1.0.0"
description = "Media Convert Service - Media processing microservice with FFmpeg and DASH conversion"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
package-mode = false

[tool.poetry.dependencies]
python = "^3.12"
celery = "^5.5.3"          
requests = "^2.32.0"       
python-decouple = "^3.8"   
loguru = "^0.7.3"          
orjson = "^3.11.1"
pydantic = "^2.11.7"       
tenacity = "^9.1.2"        
python-dateutil = "^2.9.0.post0"  
psutil = "^7.0.0"          
psycopg2-binary = "^2.9.10"
sqlalchemy = "^2.0.41"     
alembic = "^1.16.0"        
boto3 = "^1.39.13"         
defusedxml = "^0.7.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-cov = "^6.2.1"
pytest-mock = "^3.14.1"
pytest-asyncio = "^1.1.0"
pytest-watch = "^4.2.0"
ruff = "^0.12.5"
mypy = "^1.17.0"
ipython = "^9.4.0"

[tool.ruff]
line-length = 88
target-version = "py312"

[tool.ruff.lint]
select = [
    "E",    # pycodestyle
    "W",    # warnings
    "F",    # pyflakes
    "I",    # isort (ordenar imports)
    "B",    # flake8-bugbear (potenciais bugs)
    "C90",  # mccabe (complexidade ciclomática)
    "UP",   # pyupgrade (modernização de código)
    "S",    # flake8-bandit (segurança)
    "BLE",  # flake8-blind-except
    "COM",  # flake8-commas
    "ISC",  # flake8-implicit-str-concat
    "RET",  # flake8-return
    "SIM",  # flake8-simplify
    "T20",  # flake8-print (print statements)
    "PL",   # pylint (subset)
    "RUF",  # regras próprias do ruff
]
ignore = [
    "COM812"  # conflito com o formatter do Ruff
]

[tool.ruff.lint.per-file-ignores]
# Test files
"tests/**" = ["S101"]  # permitir uso de `assert` em testes

# Repository methods need multiple parameters for database operations
"app/repositories/media_conversion_repository.py" = [
    "PLR0913",  # Too many arguments - justified for repository update methods
]

# Main conversion service is inherently complex due to orchestration requirements
"app/services/conversion_service.py" = [
    "C901",     # Complex function - main orchestration method
    "PLR0912",  # Too many branches - handles multiple conversion scenarios
    "PLR0913",  # Too many arguments - main API method needs all parameters
    "PLR0915",  # Too many statements - orchestrates complete conversion flow
]

# Celery tasks need multiple parameters for microservice communication
"app/tasks/microservice_conversion.py" = [
    "PLR0913",  # Too many arguments - Celery task interface
]

# FFmpeg wrapper has inherent complexity due to multimedia processing
"app/utils/ffmpeg_wrapper.py" = [
    "S603",     # subprocess calls - controlled FFmpeg execution with validation
    "C901",     # Complex function - DASH conversion is inherently complex
    "PLR0912",  # Too many branches - handles multiple video formats/resolutions
    "PLR0915",  # Too many statements - comprehensive FFmpeg command generation
]

# Image converter has TODO code that will be implemented later
"app/services/converters/image_converter.py" = [
    "RET504",   # Unnecessary assignment - TODO code structure
]

[tool.ruff.lint.isort]
known-first-party = ["app"]
combine-as-imports = true

[tool.mypy]
python_version = "3.12"
warn_unused_configs = true
warn_return_any = true
warn_redundant_casts = true
warn_unused_ignores = true
disallow_untyped_defs = true
no_implicit_optional = true
check_untyped_defs = true
pretty = true
show_error_codes = true

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
