# SonarQube Configuration for Media Convert Microservice
sonar.projectKey=media-convert-microservice
sonar.projectName=Media Convert Microservice
sonar.projectVersion=1.0.0

# Código-fonte e testes
sonar.sources=app
sonar.tests=tests
sonar.sourceEncoding=UTF-8
sonar.python.version=3.12

# Relatórios
sonar.python.coverage.reportPaths=.reports/coverage.xml
sonar.junit.reportPaths=.reports/test-results.xml
sonar.python.ruff.reportPaths=.reports/ruff-report.json

# Exclusões de análise
sonar.exclusions=**/migrations/**,**/docker/**,**/scripts/**,**/__pycache__/**,**/temp/**,**/logs/**
sonar.coverage.exclusions=**/migrations/**,**/docker/**,**/scripts/**,**/__init__.py
sonar.cpd.exclusions=**/migrations/**,tests/**

# Log detalhado
sonar.verbose=false
